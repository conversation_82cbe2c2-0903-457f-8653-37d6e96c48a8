"""
Username Conflict Handler Module
Handles username conflicts during Gmail registration
"""

import asyncio
import logging
import random
import time
from typing import Dict, Any
from playwright.async_api import Page


class UsernameConflictHandler:
    """Handles username conflicts and resolution strategies"""
    
    async def handle_username_conflict(self, page: Page, user_data: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """
        Enhanced username conflict detection and resolution

        Args:
            page: Playwright page object
            user_data: User data dictionary containing username
            account_id: Account ID for logging

        Returns:
            Dict containing conflict resolution results
        """
        original_username = user_data['username']
        result = {
            'username_changed': False,
            'original_username': original_username,
            'final_username': original_username,
            'conflict_detected': False,
            'resolution_method': None,
            'suggestions_found': 0
        }

        try:
            # Wait for any error messages or suggestions to appear
            await asyncio.sleep(3)

            # Enhanced list of username taken error messages
            username_taken_messages = [
                'That username is taken. Try another.',
                'Username not available',
                'This username is already taken',
                'Choose a different username',
                'Sorry, this username isn\'t available',
                'Try a different username',
                'Username is not available',
                'This username isn\'t available',
                'Try another username',
                'Username already exists',
                'Please choose a different username'
            ]

            username_taken = False
            detected_message = None

            # Check for explicit error messages
            for message in username_taken_messages:
                try:
                    if await page.get_by_text(message).is_visible(timeout=1000):
                        username_taken = True
                        detected_message = message
                        logging.info(f"Username conflict detected: {message}")
                        break
                except:
                    continue

            # Check for visual indicators of username conflict
            if not username_taken:
                username_taken = await self._check_visual_conflict_indicators(page)

            if username_taken:
                result['conflict_detected'] = True
                logging.info(f"Username '{original_username}' is taken. Looking for suggestions...")

                # Take screenshot of conflict state
                await self._take_screenshot(page, account_id, "username_conflict_detected")

                # Try to find and select suggested usernames
                suggestion_success = await self._select_username_suggestion(page, user_data, result)

                if suggestion_success:
                    result['username_changed'] = True
                    result['final_username'] = user_data['username']
                    logging.info(f"✅ Username conflict resolved: '{original_username}' → '{user_data['username']}'")
                else:
                    # Fallback: generate new username
                    await self._generate_fallback_username(page, user_data, original_username, result)
                    result['username_changed'] = True
                    result['final_username'] = user_data['username']
                    logging.info(f"✅ Username conflict resolved with fallback: '{original_username}' → '{user_data['username']}'")

            return result

        except Exception as e:
            logging.error(f"Error in username conflict handling: {str(e)}")
            result['error'] = str(e)
            return result
    
    async def _check_visual_conflict_indicators(self, page: Page) -> bool:
        """Check for visual indicators of username conflict"""
        try:
            # Look for error styling or suggestion containers
            conflict_indicators = [
                '[role="alert"]',
                '.error-message',
                '[data-error="true"]',
                '.Ekjuhf',  # Google's error class
                'button[data-value*="@gmail.com"]',
                '[role="button"]:has-text("@gmail.com")',
                'button:has-text("available")',
                '.username-suggestion',
                '[aria-describedby*="error"]'
            ]

            for indicator in conflict_indicators:
                try:
                    elements = page.locator(indicator)
                    if await elements.count() > 0:
                        # Check if it's actually related to username conflict
                        for i in range(await elements.count()):
                            element_text = await elements.nth(i).text_content()
                            if element_text and any(keyword in element_text.lower()
                                                  for keyword in ['taken', 'available', 'try', 'choose', '@gmail.com']):
                                logging.info(f"Username conflict detected via indicator: {indicator}")
                                return True
                except:
                    continue
        except:
            pass
        
        return False

    async def _wait_for_suggestions(self, page: Page, max_wait_seconds: int = 10) -> bool:
        """
        Wait for username suggestions to appear on the page

        Args:
            page: Playwright page object
            max_wait_seconds: Maximum time to wait for suggestions

        Returns:
            True if suggestions appeared, False otherwise
        """
        logging.info(f"⏳ Waiting up to {max_wait_seconds}s for username suggestions to appear...")

        # Selectors that indicate suggestions are present
        suggestion_indicators = [
            'div[data-value*="@gmail.com"]',
            'button[data-value*="@gmail.com"]',
            'span[data-value*="@gmail.com"]',
            'div:has-text("@gmail.com")',
            '[role="option"]:has-text("@gmail.com")'
        ]

        for attempt in range(max_wait_seconds):
            try:
                for selector in suggestion_indicators:
                    elements = page.locator(selector)
                    count = await elements.count()
                    if count > 0:
                        # Verify it's actually a suggestion by checking text content
                        for i in range(min(count, 3)):
                            try:
                                text = await elements.nth(i).text_content()
                                if text and '@gmail.com' in text and len(text) > 10:
                                    logging.info(f"✅ Username suggestions detected after {attempt + 1}s")
                                    return True
                            except:
                                continue

                await asyncio.sleep(1)

            except Exception as e:
                logging.debug(f"Error waiting for suggestions (attempt {attempt + 1}): {e}")
                await asyncio.sleep(1)

        logging.warning(f"⚠️ No username suggestions appeared after {max_wait_seconds}s")
        return False

    async def _select_username_suggestion(self, page: Page, user_data: Dict[str, Any], result: Dict[str, Any]) -> bool:
        """
        Try to select a suggested username from Gmail's suggestions

        Args:
            page: Playwright page object
            user_data: User data dictionary to update
            result: Result dictionary to update

        Returns:
            True if suggestion was successfully selected, False otherwise
        """
        try:
            logging.info("🔍 Looking for username suggestions...")

            # Wait for suggestions to appear with multiple attempts
            suggestions_appeared = await self._wait_for_suggestions(page)
            if not suggestions_appeared:
                logging.warning("⚠️ No suggestions appeared after waiting")

            # Take screenshot for debugging
            await self._take_screenshot(page, 999, "username_suggestions_debug")

            # Modern Gmail suggestion selectors (updated for 2025)
            suggestion_selectors = [
                # Primary selectors for Gmail suggestions
                'div[data-value*="@gmail.com"]',  # Most common in 2025
                'button[data-value*="@gmail.com"]',
                'span[data-value*="@gmail.com"]',
                '[role="option"][data-value*="@gmail.com"]',

                # Alternative selectors
                'div:has-text("@gmail.com")',
                'button:has-text("@gmail.com")',
                'span:has-text("@gmail.com")',

                # Generic suggestion containers
                '[data-suggestion-index]',
                '.suggestion-item',
                '.username-suggestion',

                # Material Design components
                '.VfPpkd-LgbsSe:has-text("@gmail.com")',
                '.VfPpkd-rymPhb:has-text("@gmail.com")',

                # Fallback selectors
                '[jsaction*="click"]:has-text("@gmail.com")',
                'div[role="button"]:has-text("@gmail.com")',
                'button[aria-label*="available"]'
            ]

            suggestions_found = 0
            selected_suggestion = None

            # Try each selector
            for i, selector in enumerate(suggestion_selectors):
                try:
                    logging.info(f"🔍 Trying selector {i+1}/{len(suggestion_selectors)}: {selector}")
                    suggestions = page.locator(selector)
                    count = await suggestions.count()

                    if count > 0:
                        suggestions_found = max(suggestions_found, count)
                        logging.info(f"✅ Found {count} elements with selector: {selector}")

                        # Get all suggestion texts for analysis
                        all_suggestions = []
                        valid_suggestions = []

                        for j in range(min(count, 10)):  # Check up to 10 suggestions
                            try:
                                element = suggestions.nth(j)

                                # Try different ways to get the text
                                text_methods = [
                                    lambda el: el.text_content(),
                                    lambda el: el.get_attribute('data-value'),
                                    lambda el: el.get_attribute('aria-label'),
                                    lambda el: el.inner_text()
                                ]

                                suggestion_text = None
                                for method in text_methods:
                                    try:
                                        text = await method(element)
                                        if text and '@gmail.com' in text:
                                            suggestion_text = text.strip()
                                            break
                                    except:
                                        continue

                                if suggestion_text:
                                    all_suggestions.append(suggestion_text)
                                    # Check if it's a valid Gmail suggestion
                                    if '@gmail.com' in suggestion_text and len(suggestion_text) > 10:
                                        valid_suggestions.append((j, element, suggestion_text))

                            except Exception as e:
                                logging.debug(f"Error getting suggestion {j}: {e}")
                                continue

                        if all_suggestions:
                            logging.info(f"📋 All found suggestions: {all_suggestions}")

                        if valid_suggestions:
                            logging.info(f"✅ Valid Gmail suggestions: {[s[2] for s in valid_suggestions]}")

                            # Select the first valid suggestion
                            _, element, suggestion_text = valid_suggestions[0]
                            logging.info(f"🎯 Selecting suggestion: {suggestion_text}")

                            # Try to click the suggestion
                            try:
                                await element.click()
                                await asyncio.sleep(2)  # Wait for selection to register
                                logging.info(f"✅ Successfully clicked suggestion: {suggestion_text}")
                            except Exception as click_error:
                                logging.warning(f"Failed to click suggestion, trying alternative methods: {click_error}")
                                # Try alternative click methods
                                try:
                                    await element.dispatch_event('click')
                                    await asyncio.sleep(2)
                                    logging.info(f"✅ Successfully dispatched click to suggestion: {suggestion_text}")
                                except Exception as dispatch_error:
                                    logging.error(f"All click methods failed: {dispatch_error}")
                                    continue

                            # Extract username from suggestion
                            if '@gmail.com' in suggestion_text:
                                new_username = suggestion_text.replace('@gmail.com', '').strip()
                            else:
                                new_username = suggestion_text.strip()

                            # Update user data
                            user_data['username'] = new_username
                            selected_suggestion = suggestion_text

                            # Update result
                            result['resolution_method'] = 'suggestion_selected'
                            result['suggestions_found'] = suggestions_found
                            result['selected_suggestion'] = selected_suggestion

                            logging.info(f"✅ Successfully selected suggestion: {new_username}")
                            return True

                except Exception as e:
                    logging.debug(f"Suggestion selector {selector} failed: {str(e)}")
                    continue

            result['suggestions_found'] = suggestions_found
            if suggestions_found > 0:
                logging.warning(f"Found {suggestions_found} suggestions but couldn't select any")
            else:
                logging.warning("No username suggestions found")

            return False

        except Exception as e:
            logging.error(f"Error selecting username suggestion: {str(e)}")
            return False
    
    async def _take_screenshot(self, page: Page, account_id: int, step: str):
        """Take screenshot for debugging"""
        try:
            from config import Config
            screenshot_path = Config.SCREENSHOTS_DIR / f"account_{account_id}_{step}.png"
            await page.screenshot(path=str(screenshot_path))
            logging.info(f"Screenshot saved: {screenshot_path}")
        except Exception as e:
            logging.error(f"Failed to take screenshot: {e}")

    async def _generate_fallback_username(self, page: Page, user_data: Dict[str, Any], original_username: str, result: Dict[str, Any]) -> None:
        """
        Generate a fallback username when suggestions are not available

        Args:
            page: Playwright page object
            user_data: User data dictionary to update
            original_username: Original username that was taken
            result: Result dictionary to update
        """
        try:
            # Try multiple fallback strategies
            fallback_strategies = [
                lambda u: f"{u}{random.randint(100, 999)}",
                lambda u: f"{u}.{random.randint(10, 99)}",
                lambda u: f"{u}{random.randint(1000, 9999)}",
                lambda u: f"{u}.{random.randint(1, 9)}",
                lambda u: f"{u}{random.randint(10, 99)}",
            ]

            for i, strategy in enumerate(fallback_strategies):
                try:
                    new_username = strategy(original_username)
                    logging.info(f"Trying fallback username strategy {i+1}: {new_username}")

                    # Clear the username field and enter new username
                    username_selectors = [
                        ('input[name="username"]', 'name="username"'),
                        ('input[aria-label*="Username"]', 'aria-label contains Username'),
                        ('input[aria-label*="Gmail"]', 'aria-label contains Gmail'),
                        ('input[type="text"]:not([readonly]):not([disabled])', 'enabled text input')
                    ]

                    username_field = None
                    for selector, description in username_selectors:
                        try:
                            field = page.locator(selector).first
                            if await field.is_visible():
                                # Verify it's actually an input field
                                tag_name = await field.evaluate('el => el.tagName.toLowerCase()')
                                if tag_name == 'input':
                                    username_field = field
                                    logging.info(f"Found username field with: {description}")
                                    break
                        except Exception as e:
                            logging.debug(f"Selector {description} failed: {e}")
                            continue

                    if username_field:
                        try:
                            await username_field.clear()
                            await username_field.fill(new_username)

                            # Verify the value was set correctly
                            filled_value = await username_field.input_value()
                            if filled_value == new_username:
                                user_data['username'] = new_username
                                result['resolution_method'] = f'fallback_strategy_{i+1}'
                                result['fallback_username'] = new_username
                                logging.info(f"✅ Generated fallback username: {new_username}")
                                return
                            else:
                                logging.warning(f"⚠️ Fallback username value mismatch. Expected: {new_username}, Got: {filled_value}")
                        except Exception as e:
                            logging.error(f"Error filling fallback username: {e}")
                    else:
                        logging.warning("Could not find username field for fallback")

                except Exception as e:
                    logging.debug(f"Fallback strategy {i+1} failed: {str(e)}")
                    continue

            # Last resort: use original username with timestamp
            timestamp = str(int(time.time()))[-4:]  # Last 4 digits of timestamp
            final_username = f"{original_username}{timestamp}"
            user_data['username'] = final_username
            result['resolution_method'] = 'timestamp_fallback'
            result['fallback_username'] = final_username

            logging.info(f"✅ Using timestamp fallback username: {final_username}")

        except Exception as e:
            logging.error(f"Error generating fallback username: {str(e)}")
            # Keep original username as last resort
            result['resolution_method'] = 'no_change'
