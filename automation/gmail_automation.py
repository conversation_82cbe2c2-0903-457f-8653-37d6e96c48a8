"""
Gmail Automation Module
Handles browser automation for Gmail registration process
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any
from playwright.async_api import Page, async_playwright

from constants import Messages, URLs
from .username_handler import UsernameConflictHandler


class GmailAutomation:
    """Handles Gmail registration browser automation"""
    
    def __init__(self, config, screenshot_handler=None):
        self.config = config
        self.screenshot_handler = screenshot_handler
        self.username_handler = UsernameConflictHandler()
    
    def get_month_mapping(self) -> Dict[str, str]:
        """Get month mapping for different locales and formats"""
        return {
            # Standard month names
            "January": "January", "February": "February", "March": "March",
            "April": "April", "May": "May", "June": "June",
            "July": "July", "August": "August", "September": "September",
            "October": "October", "November": "November", "December": "December",
            # Abbreviated month names
            "Jan": "January", "Feb": "February", "Mar": "March", "Apr": "April",
            "Jun": "June", "Jul": "July", "Aug": "August", "Sep": "September",
            "Oct": "October", "Nov": "November", "Dec": "December",
            # Numeric month values
            "1": "January", "2": "February", "3": "March", "4": "April",
            "5": "May", "6": "June", "7": "July", "8": "August",
            "9": "September", "10": "October", "11": "November", "12": "December"
        }
    
    async def perform_registration(self, user_data: Dict[str, Any], browser_profile: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """
        Perform the actual Gmail registration process using Playwright
        
        Args:
            user_data: User data dict
            browser_profile: Browser profile dict
            account_id: Account ID
            
        Returns:
            Registration result dict
        """
        # Track username changes for logging
        original_username = user_data['username']
        username_conflict_history = []

        try:
            print(Messages.CONSOLE["browser_start"])
            
            async with async_playwright() as p:
                # Launch browser with stealth mode
                browser = await p.chromium.launch(
                    headless=False,  # Set to True for production
                    proxy=browser_profile.get('proxy'),
                    args=[
                        '--disable-blink-features=AutomationControlled',
                        '--disable-features=IsolateOrigins,site-per-process',
                        '--disable-site-isolation-trials',
                        f'--window-size={browser_profile["viewport"]["width"]},{browser_profile["viewport"]["height"]}',
                        '--start-maximized'
                    ]
                )
                
                # Create new context with custom viewport and timezone
                context = await browser.new_context(
                    viewport=browser_profile['viewport'],
                    timezone_id=browser_profile['timezone_id'],
                    user_agent=browser_profile['user_agent'],
                    locale=browser_profile['locale'],
                    geolocation=browser_profile.get('geolocation'),
                    permissions=['geolocation']
                )
                
                # Add stealth scripts
                await context.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['en-US', 'en']
                    });
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });
                """)
                
                # Create new page
                page = await context.new_page()
                
                try:
                    # Step 1: Navigate to Gmail signup page
                    logging.info("Step 1: Navigating to signup page")
                    await page.goto(URLs.GMAIL_SIGNUP)
                    await self._take_screenshot(page, account_id, "step1")
                    await self._random_delay()
                    
                    # Step 2: Fill basic information
                    await self._fill_basic_information(page, user_data, account_id)
                    
                    # Step 3: Fill personal information
                    await self._fill_personal_information(page, user_data, account_id)
                    
                    # Step 4: Handle username and password
                    username_conflict_result = await self._handle_username_and_password(
                        page, user_data, account_id, username_conflict_history
                    )
                    
                    # Check for captcha
                    captcha_result = await self._check_for_captcha(page, user_data, account_id)
                    if captcha_result:
                        return captcha_result
                    
                    # Check for success
                    result = await self._check_registration_success(
                        page, user_data, original_username, username_conflict_history, account_id
                    )
                    
                except Exception as e:
                    result = await self._handle_automation_error(page, user_data, account_id, e, original_username, username_conflict_history)
                
                finally:
                    await context.close()
                    await browser.close()
                
                return result

        except Exception as e:
            logging.error(f"An error occurred during browser automation: {e}")
            import traceback
            logging.error(f"Full traceback: {traceback.format_exc()}")

            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__,
                "user_data": user_data,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _fill_basic_information(self, page: Page, user_data: Dict[str, Any], account_id: int):
        """Fill basic information (first name, last name)"""
        logging.info("Step 2: Filling basic information")
        
        # Fill First name
        await page.get_by_role('textbox', name='First name').fill(user_data['first_name'])
        await self._random_delay()
        
        # Fill Last name
        await page.get_by_role('textbox', name='Last name (optional)').fill(user_data['last_name'])
        await self._random_delay()
        
        # Click Next
        await page.get_by_role('button', name='Next').click()
        await self._take_screenshot(page, account_id, "step2")
        await self._random_delay()
    
    async def _fill_personal_information(self, page: Page, user_data: Dict[str, Any], account_id: int):
        """Fill personal information (birth date, gender)"""
        logging.info("Step 3: Filling personal information")

        # Select month
        month_mapping = self.get_month_mapping()
        user_month = month_mapping.get(str(user_data['birth_month']), str(user_data['birth_month']))
        logging.info(f"Selecting month: {user_month} (from {user_data['birth_month']})")
        await page.get_by_role('combobox', name='Month').click()
        await page.get_by_role('option', name=user_month).click()
        await self._random_delay()

        # Enter day
        logging.info(f"Entering day: {user_data['birth_day']}")
        await page.get_by_role('textbox', name='Day').fill(str(user_data['birth_day']))
        await self._random_delay()

        # Enter year
        logging.info(f"Entering year: {user_data['birth_year']}")
        await page.get_by_role('textbox', name='Year').fill(str(user_data['birth_year']))
        await self._random_delay()

        # Select gender
        logging.info("Opening gender dropdown")
        await page.get_by_role('combobox', name='Gender').click()
        await self._random_delay()

        # Click on gender option with exact match
        gender_value = user_data['gender']  # Should be "Male" or "Female"
        logging.info(f"Selecting gender: {gender_value}")
        await page.get_by_role('option', name=gender_value, exact=True).click()
        await self._random_delay()

        # Click Next
        logging.info("Clicking Next after personal information")
        await page.get_by_role('button', name='Next').click()
        await self._take_screenshot(page, account_id, "step3")
        await self._random_delay()

        # Wait for next page to load
        await page.wait_for_load_state('networkidle', timeout=10000)
        logging.info("Page loaded after personal information step")
    
    async def _random_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """Add random delay between actions to simulate human behavior"""
        import random
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    async def _take_screenshot(self, page: Page, account_id: int, step: str):
        """Take screenshot of current page state"""
        if self.screenshot_handler:
            await self.screenshot_handler.take_screenshot(page, account_id, step)
        else:
            screenshot_path = self.config.SCREENSHOTS_DIR / f"account_{account_id}_{step}.png"
            await page.screenshot(path=str(screenshot_path))
            logging.info(f"Screenshot saved: {screenshot_path}")

    async def _handle_username_and_password(self, page: Page, user_data: Dict[str, Any], account_id: int, username_conflict_history: list) -> Dict[str, Any]:
        """Handle username selection and password entry"""
        logging.info("Step 4: Filling username and password")

        # Check if we're on the "Choose your Gmail address" page (username suggestions)
        page_title = await page.title()
        current_url = page.url
        logging.info(f"Current page: {page_title}")
        logging.info(f"Current URL: {current_url}")

        if "Choose your Gmail address" in page_title or "Pick a Gmail address" in page_title:
            await self._handle_gmail_address_selection(page, user_data, account_id)

        # Try to find and fill username input field
        await self._fill_username_field(page, user_data)

        # Handle username conflicts
        username_conflict_result = await self.username_handler.handle_username_conflict(page, user_data, account_id)
        if username_conflict_result['username_changed']:
            username_conflict_history.append(username_conflict_result)
            logging.info(f"Username changed from '{username_conflict_result['original_username']}' to '{user_data['username']}' due to conflict")
            await self._take_screenshot(page, account_id, "username_conflict_resolved")

        # Click Next to proceed to password page
        logging.info("Clicking Next to proceed to password page")
        await page.get_by_role('button', name='Next').click()
        await self._random_delay()

        # Wait for page to load and check if we moved to password page
        await page.wait_for_load_state('networkidle', timeout=10000)
        current_url = page.url
        logging.info(f"Current URL after Next click: {current_url}")

        # Handle additional username conflicts if still on username page
        if 'username' in current_url:
            await self._handle_persistent_username_conflicts(page, user_data, account_id, username_conflict_history)

        logging.info("Password page loaded")
        await self._take_screenshot(page, account_id, "step4_password_page")

        # Fill password fields
        await self._fill_password_fields(page, user_data)

        # Click Next
        await page.get_by_role('button', name='Next').click()
        await self._take_screenshot(page, account_id, "step4")
        await self._random_delay()

        return {"success": True}

    async def _handle_gmail_address_selection(self, page: Page, user_data: Dict[str, Any], account_id: int):
        """Handle Gmail address selection page"""
        logging.info("On Gmail address selection page - looking for suggestions or create option")

        # Look for "Create your own Gmail address" option
        create_own_selectors = [
            'text="Create your own Gmail address"',
            'text="Create a different address"',
            '[aria-label="Create your own Gmail address"]',
            'button:has-text("Create")',
            'text="Create"'
        ]

        create_clicked = False
        for selector_str in create_own_selectors:
            try:
                logging.info(f"Trying create own selector: {selector_str}")
                if selector_str.startswith('text='):
                    text_content = selector_str.replace('text=', '').strip('"')
                    selector = page.get_by_text(text_content)
                elif 'aria-label' in selector_str:
                    selector = page.locator(selector_str)
                else:
                    selector = page.locator(selector_str)

                if await selector.is_visible(timeout=2000):
                    await selector.click()
                    create_clicked = True
                    logging.info(f"Clicked create own address with: {selector_str}")
                    await self._random_delay()
                    break
            except Exception as e:
                logging.info(f"Create own selector {selector_str} failed: {str(e)}")
                continue

        if not create_clicked:
            # If can't find create option, try to select a suggested username
            await self._select_suggested_username(page, user_data)

        if not create_clicked:
            raise Exception("Could not find create option or username suggestions")

        # Wait for page to load after selection
        await page.wait_for_load_state('networkidle', timeout=10000)

    async def _select_suggested_username(self, page: Page, user_data: Dict[str, Any]):
        """Select a suggested username if create option is not available"""
        logging.info("Could not find create option, looking for suggested usernames")
        suggestion_selectors = [
            'button[data-value*="@gmail.com"]',
            '[role="button"]:has-text("@gmail.com")',
            'button:has-text("available")',
            '.username-suggestion',
            'button[jsaction]'
        ]

        for selector_str in suggestion_selectors:
            try:
                suggestions = page.locator(selector_str)
                count = await suggestions.count()
                if count > 0:
                    first_suggestion = suggestions.first
                    suggestion_text = await first_suggestion.text_content()
                    logging.info(f"Selecting suggested username: {suggestion_text}")
                    await first_suggestion.click()

                    # Update username
                    if '@gmail.com' in suggestion_text:
                        user_data['username'] = suggestion_text.replace('@gmail.com', '')
                    else:
                        user_data['username'] = suggestion_text

                    return True
            except Exception as e:
                logging.info(f"Suggestion selector {selector_str} failed: {str(e)}")
                continue

        return False

    async def _fill_username_field(self, page: Page, user_data: Dict[str, Any]):
        """Fill the username input field with improved detection"""
        logging.info(f"🔍 Looking for username field to fill with: {user_data['username']}")

        # Modern Gmail username field selectors (updated for 2025)
        username_selectors = [
            # Primary selectors for Gmail username input
            ('input[name="username"]', 'name="username"'),
            ('input[aria-label*="Username"]', 'aria-label contains Username'),
            ('input[aria-label*="Gmail"]', 'aria-label contains Gmail'),
            ('input[placeholder*="username"]', 'placeholder contains username'),
            ('input[placeholder*="Username"]', 'placeholder contains Username'),

            # Playwright semantic selectors
            ('get_by_label("Username")', 'get_by_label Username'),
            ('get_by_label("Create a Gmail address")', 'get_by_label Create Gmail'),
            ('get_by_role("textbox", name="Username")', 'get_by_role textbox Username'),
            ('get_by_placeholder("Username")', 'get_by_placeholder Username'),

            # Fallback selectors (more specific)
            ('input[type="text"][data-initial-value=""]', 'text input with empty initial value'),
            ('input[type="text"]:not([readonly]):not([disabled])', 'enabled text input'),
        ]

        username_filled = False
        for selector_str, description in username_selectors:
            try:
                logging.info(f"🔍 Trying username selector: {description}")

                # Handle different selector types
                if selector_str.startswith('get_by_label'):
                    if 'Username' in selector_str:
                        selector = page.get_by_label('Username')
                    else:
                        selector = page.get_by_label('Create a Gmail address')
                elif selector_str.startswith('get_by_role'):
                    selector = page.get_by_role('textbox', name='Username')
                elif selector_str.startswith('get_by_placeholder'):
                    selector = page.get_by_placeholder('Username')
                else:
                    selector = page.locator(selector_str)

                # Wait for element to be visible
                await selector.wait_for(state='visible', timeout=5000)

                # Verify it's actually an input field
                tag_name = await selector.evaluate('el => el.tagName.toLowerCase()')
                if tag_name != 'input':
                    logging.warning(f"⚠️ Element is not an input field: {tag_name}")
                    continue

                # Verify it's not readonly or disabled
                is_readonly = await selector.evaluate('el => el.readOnly')
                is_disabled = await selector.evaluate('el => el.disabled')
                if is_readonly or is_disabled:
                    logging.warning(f"⚠️ Input field is readonly({is_readonly}) or disabled({is_disabled})")
                    continue

                # Clear and fill the field
                await selector.clear()
                await selector.fill(user_data['username'])

                # Verify the value was set correctly
                filled_value = await selector.input_value()
                if filled_value == user_data['username']:
                    username_filled = True
                    logging.info(f"✅ Successfully filled username field: {description}")
                    logging.info(f"✅ Verified username value: {filled_value}")
                    break
                else:
                    logging.warning(f"⚠️ Username value mismatch. Expected: {user_data['username']}, Got: {filled_value}")

            except Exception as e:
                logging.debug(f"Username selector {description} failed: {str(e)}")
                continue

        if not username_filled:
            # Take screenshot for debugging
            await self._take_screenshot(page, 999, "username_field_not_found")
            raise Exception("Could not find or fill username field with any selector")

        await self._random_delay()

    async def _handle_persistent_username_conflicts(self, page: Page, user_data: Dict[str, Any], account_id: int, username_conflict_history: list):
        """Handle username conflicts that persist after clicking Next"""
        logging.warning("Still on username page - username might be taken or there's an error")

        # Try to find and handle any error messages or suggestions
        await asyncio.sleep(2)

        # Look for any visible error messages
        error_elements = page.locator('[role="alert"], .error-message, [data-error], .Ekjuhf')
        error_count = await error_elements.count()
        username_taken_detected = False

        if error_count > 0:
            for i in range(error_count):
                error_text = await error_elements.nth(i).text_content()
                logging.warning(f"Error message found: {error_text}")
                if "taken" in error_text.lower() or "not available" in error_text.lower():
                    username_taken_detected = True
                elif "only letters" in error_text.lower() or "periods" in error_text.lower():
                    # Username format error - fix the username
                    await self._fix_username_format(page, user_data)
                    return

        # Check for specific username taken messages
        username_taken_messages = [
            'That username is taken. Try another.',
            'Username not available',
            'This username is already taken',
            'Choose a different username',
            'Sorry, this username isn\'t available',
            'Try a different username'
        ]

        for message in username_taken_messages:
            try:
                if await page.get_by_text(message).is_visible(timeout=1000):
                    username_taken_detected = True
                    logging.warning(f"Username taken message detected: {message}")
                    break
            except:
                continue

        if username_taken_detected:
            # Use enhanced username conflict handling
            secondary_conflict_result = await self.username_handler.handle_username_conflict(page, user_data, account_id)
            if secondary_conflict_result['username_changed']:
                username_conflict_history.append(secondary_conflict_result)
                logging.info(f"Secondary username conflict resolved: '{secondary_conflict_result['original_username']}' → '{user_data['username']}'")
                await self._take_screenshot(page, account_id, "secondary_username_conflict_resolved")

            # Click Next again after handling username taken
            await self._random_delay()
            await page.get_by_role('button', name='Next').click()
            await page.wait_for_load_state('networkidle', timeout=10000)
            current_url = page.url
            logging.info(f"URL after handling username taken: {current_url}")
        else:
            logging.info("No username taken error detected, but still on username page")

    async def _fix_username_format(self, page: Page, user_data: Dict[str, Any]):
        """Fix username format errors"""
        logging.warning("Username format error detected, fixing username")
        original_username = user_data['username']
        # Remove invalid characters and replace with valid ones
        fixed_username = original_username.replace('_', '.').replace('-', '.')
        # Remove any other invalid characters
        import re
        fixed_username = re.sub(r'[^a-z0-9.]', '', fixed_username)

        # If username is too short after cleaning, add numbers
        if len(fixed_username) < 6:
            import random
            fixed_username += str(random.randint(100, 999))

        logging.info(f"Fixed username from {original_username} to {fixed_username}")

        # Clear and refill the username field
        username_field = page.get_by_label('Username')
        await username_field.clear()
        await username_field.fill(fixed_username)
        user_data['username'] = fixed_username

        # Click Next again
        await self._random_delay()
        await page.get_by_role('button', name='Next').click()
        await page.wait_for_load_state('networkidle', timeout=10000)
        current_url = page.url
        logging.info(f"URL after fixing username format: {current_url}")

        # Check if we moved to password page
        if 'password' in current_url or 'username' not in current_url:
            logging.info("Successfully moved to password page after fixing username")
        else:
            logging.warning("Still on username page after fixing format")

    async def _fill_password_fields(self, page: Page, user_data: Dict[str, Any]):
        """Fill password fields"""
        password_selectors = [
            'locator("input[type=\'password\']")',
            'get_by_label("Password")',
            'get_by_placeholder("Password")',
            'locator("input[name=\'password\']")'
        ]

        password_filled = False
        for selector_str in password_selectors:
            try:
                logging.info(f"Trying password selector: {selector_str}")
                if 'type=\'password\'' in selector_str:
                    password_fields = page.locator('input[type="password"]')
                    count = await password_fields.count()
                    if count >= 1:
                        await password_fields.first.fill(user_data['password'])
                        if count >= 2:
                            await password_fields.last.fill(user_data['password'])
                        password_filled = True
                        logging.info(f"Successfully filled password fields (found {count} fields)")
                        break
                elif 'get_by_label' in selector_str:
                    selector = page.get_by_label('Password')
                    await selector.wait_for(state='visible', timeout=5000)
                    await selector.fill(user_data['password'])
                    password_filled = True
                    break
                elif 'get_by_placeholder' in selector_str:
                    selector = page.get_by_placeholder('Password')
                    await selector.wait_for(state='visible', timeout=5000)
                    await selector.fill(user_data['password'])
                    password_filled = True
                    break
                elif 'name=\'password\'' in selector_str:
                    selector = page.locator('input[name="password"]')
                    await selector.wait_for(state='visible', timeout=5000)
                    await selector.fill(user_data['password'])
                    password_filled = True
                    break
            except Exception as e:
                logging.info(f"Password selector {selector_str} failed: {str(e)}")
                continue

        if not password_filled:
            raise Exception("Could not find password field with any selector")

        await self._random_delay()

    async def _check_for_captcha(self, page: Page, user_data: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """Check for captcha or verification requirements"""
        try:
            # Check if we're on captcha/verification page
            page_title = await page.title()
            current_url = page.url

            if "robot" in page_title.lower() or "captcha" in page_title.lower() or "verify" in page_title.lower():
                logging.warning(f"Captcha/verification page detected: {page_title}")
                await self._take_screenshot(page, account_id, "captcha_page")
                return {
                    "success": False,
                    "error": f"Captcha/verification required: {page_title}",
                    "user_data": user_data,
                    "timestamp": datetime.now().isoformat()
                }

            # Also check for reCAPTCHA iframe
            captcha_iframe = page.locator('iframe[title="reCAPTCHA"]')
            if await captcha_iframe.count() > 0:
                logging.warning("reCAPTCHA iframe detected")
                await self._take_screenshot(page, account_id, "recaptcha")
                return {
                    "success": False,
                    "error": "reCAPTCHA detected",
                    "user_data": user_data,
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logging.info(f"Error checking for captcha: {str(e)}")

        return None

    async def _check_registration_success(self, page: Page, user_data: Dict[str, Any], original_username: str, username_conflict_history: list, account_id: int) -> Dict[str, Any]:
        """Check if registration was successful"""
        if await page.get_by_text('Welcome to Google').is_visible():
            email = f"{user_data['username']}@gmail.com"
            result = {
                "success": True,
                "email": email,
                "user_data": user_data,
                "timestamp": datetime.now().isoformat(),
                "screenshots_path": str(self.config.SCREENSHOTS_DIR / f"account_{account_id}"),
                "username_conflicts": {
                    "original_username": original_username,
                    "final_username": user_data['username'],
                    "username_changed": original_username != user_data['username'],
                    "conflict_history": username_conflict_history,
                    "total_conflicts": len(username_conflict_history)
                }
            }

            # Log username change information
            if original_username != user_data['username']:
                logging.info(f"🔄 Username was changed during registration:")
                logging.info(f"   Original: {original_username}")
                logging.info(f"   Final: {user_data['username']}")
                logging.info(f"   Total conflicts resolved: {len(username_conflict_history)}")
                for i, conflict in enumerate(username_conflict_history, 1):
                    logging.info(f"   Conflict {i}: {conflict.get('resolution_method', 'unknown')} - {conflict.get('suggestions_found', 0)} suggestions found")
            else:
                logging.info(f"✅ Username '{original_username}' was available without conflicts")
        else:
            result = {
                "success": False,
                "error": "Registration failed or blocked",
                "user_data": user_data,
                "timestamp": datetime.now().isoformat(),
                "username_conflicts": {
                    "original_username": original_username,
                    "final_username": user_data['username'],
                    "username_changed": original_username != user_data['username'],
                    "conflict_history": username_conflict_history,
                    "total_conflicts": len(username_conflict_history)
                }
            }

        return result

    async def _handle_automation_error(self, page: Page, user_data: Dict[str, Any], account_id: int, error: Exception, original_username: str, username_conflict_history: list) -> Dict[str, Any]:
        """Handle errors that occur during automation"""
        logging.error(f"Error during automation: {str(error)}")
        logging.error(f"Error type: {type(error).__name__}")

        # Take screenshot for debugging
        await self._take_screenshot(page, account_id, "error")

        # Try to get page title and URL for debugging
        try:
            page_title = await page.title()
            page_url = page.url
            logging.error(f"Error occurred on page: {page_title} ({page_url})")
        except:
            logging.error("Could not get page title/URL")

        # Try to get page content for debugging
        try:
            page_content = await page.content()
            # Save page HTML for debugging
            html_path = self.config.SCREENSHOTS_DIR / f"account_{account_id}_error.html"
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(page_content)
            logging.error(f"Page HTML saved to: {html_path}")
        except:
            logging.error("Could not save page HTML")

        return {
            "success": False,
            "error": str(error),
            "error_type": type(error).__name__,
            "user_data": user_data,
            "timestamp": datetime.now().isoformat(),
            "username_conflicts": {
                "original_username": original_username,
                "final_username": user_data['username'],
                "username_changed": original_username != user_data['username'],
                "conflict_history": username_conflict_history,
                "total_conflicts": len(username_conflict_history)
            }
        }
